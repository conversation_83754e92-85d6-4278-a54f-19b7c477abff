from decimal import Decimal
from django.db import models
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import FileExtensionValidator
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils.translation import gettext_lazy as _
from model_utils import FieldTracker

from customers.models import Customer
from inventory.models import Product
from accounts.models import Salesperson
from accounts.utils import send_notification

# Constants for status messages
STATUS_MESSAGES = {
    'pending': _('قيد الانتظار'),
    'processing': _('قيد المعالجة'),
    'completed': _('مكتمل'),
    'cancelled': _('ملغي'),
}

class OrderType(models.Model):
    ORDER_TYPE_ACCESSORY = 'ACCESSORY'
    ORDER_TYPE_INSTALLATION = 'INSTALLATION'
    ORDER_TYPE_INSPECTION = 'INSPECTION'
    ORDER_TYPE_TAILORING = 'TAILORING'

    ORDER_TYPE_CHOICES = [
        (ORDER_TYPE_ACCESSORY, 'اكسسوار'),
        (ORDER_TYPE_INSTALLATION, 'تركيب'),
        (ORDER_TYPE_INSPECTION, 'معاينة'),
        (ORDER_TYPE_TAILORING, 'تفصيل'),
    ]
    
    name = models.CharField(
        max_length=20,
        choices=ORDER_TYPE_CHOICES,
        unique=True,
        verbose_name=_("نوع الطلب")
    )
    requires_contract = models.BooleanField(
        default=False,
        verbose_name=_("يتطلب عقد")
    )

    class Meta:
        verbose_name = _("نوع الطلب")
        verbose_name_plural = _("أنواع الطلبات")

    def __str__(self):
        return dict(self.ORDER_TYPE_CHOICES).get(self.name, self.name)

class Order(models.Model):
    STATUS_PENDING = 'pending'
    STATUS_PROCESSING = 'processing'
    STATUS_COMPLETED = 'completed'
    STATUS_CANCELLED = 'cancelled'

    STATUS_CHOICES = [
        (STATUS_PENDING, 'قيد الانتظار'),
        (STATUS_PROCESSING, 'قيد المعالجة'),
        (STATUS_COMPLETED, 'مكتمل'),
        (STATUS_CANCELLED, 'ملغي'),
    ]

    TRACKING_STATUS_PENDING = 'pending'
    TRACKING_STATUS_IN_PROGRESS = 'in_progress'
    TRACKING_STATUS_READY = 'ready'
    TRACKING_STATUS_DELIVERED = 'delivered'

    TRACKING_STATUS_CHOICES = [
        (TRACKING_STATUS_PENDING, 'قيد الانتظار'),
        (TRACKING_STATUS_IN_PROGRESS, 'جاري التنفيذ'),
        (TRACKING_STATUS_READY, 'جاهز للتسليم'),
        (TRACKING_STATUS_DELIVERED, 'تم التسليم'),
    ]

    DELIVERY_METHOD_BRANCH = 'BRANCH'
    DELIVERY_METHOD_HOME = 'HOME'

    DELIVERY_CHOICES = [
        (DELIVERY_METHOD_BRANCH, 'استلام من الفرع'),
        (DELIVERY_METHOD_HOME, 'توصيل للمنزل'),
    ]

    # Customer Information
    customer = models.ForeignKey(
        'customers.Customer',
        on_delete=models.CASCADE,
        verbose_name=_('العميل')
    )

    # Basic Information
    order_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('رقم الطلب')
    )
    order_date = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الطلب')
    )
    order_type = models.ForeignKey(
        OrderType,
        on_delete=models.CASCADE,
        verbose_name=_("نوع الطلب")
    )
    
    # Contract Information
    contract_number = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("رقم العقد")
    )
    contract_file = models.FileField(
        upload_to='contracts/',
        null=True,
        blank=True,
        validators=[FileExtensionValidator(allowed_extensions=['pdf'])],
        verbose_name=_("ملف العقد")
    )

    # Delivery Information
    delivery_method = models.CharField(
        max_length=10,
        choices=DELIVERY_CHOICES,
        default='BRANCH',
        verbose_name="طريقة التسليم"
    )
    delivery_address = models.TextField(
        null=True,
        blank=True,
        verbose_name="عنوان التسليم"
    )

    # Status Information
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='حالة الطلب'
    )
    tracking_status = models.CharField(
        max_length=20,
        choices=TRACKING_STATUS_CHOICES,
        default='pending',
        verbose_name='حالة التتبع'
    )

    # Financial Information
    invoice_number = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name='رقم الفاتورة'
    )
    payment_verified = models.BooleanField(
        default=False,
        verbose_name='تم التحقق من السداد'
    )
    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name='المبلغ الإجمالي'
    )
    paid_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name='المبلغ المدفوع'
    )
    final_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="السعر النهائي"
    )

    # Relationships
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name='تم الإنشاء بواسطة'
    )
    branch = models.ForeignKey(
        'accounts.Branch',
        on_delete=models.CASCADE,
        related_name='orders',
        verbose_name='الفرع',
        null=True
    )

    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )
    last_notification_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='تاريخ آخر إشعار'
    )

    # Additional Fields
    notes = models.TextField(
        blank=True,
        verbose_name='ملاحظات'
    )
    selected_types = models.JSONField(
        default=list,
        verbose_name='أنواع الطلب المختارة'
    )
    service_types = models.JSONField(
        default=list,
        blank=True,
        verbose_name='أنواع الخدمات'
    )

    # Tracking
    tracker = FieldTracker(fields=['tracking_status', 'status'])

    class Meta:
        verbose_name = 'طلب'
        verbose_name_plural = 'الطلبات'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['order_number']),
            models.Index(fields=['tracking_status']),
            models.Index(fields=['created_at']),
            models.Index(fields=['branch', 'tracking_status']),
            models.Index(fields=['payment_verified']),
        ]

    def clean(self):
        """التحقق من صحة البيانات"""
        if not self.customer:
            raise ValidationError({'customer': _('يجب اختيار العميل')})

        if self.order_type:
            order_type_name = self.order_type.name
            
            # التحقق من العقد للأنواع التي تتطلب عقداً
            if order_type_name in [
                OrderType.ORDER_TYPE_INSTALLATION,
                OrderType.ORDER_TYPE_TAILORING,
                OrderType.ORDER_TYPE_ACCESSORY
            ]:
                if not self.contract_number:
                    raise ValidationError({
                        'contract_number': _('رقم العقد مطلوب لهذا النوع من الطلبات')
                    })
                if not self.contract_file:
                    raise ValidationError({
                        'contract_file': _('ملف العقد مطلوب لهذا النوع من الطلبات')
                    })

            # التحقق من طريقة التسليم
            if order_type_name == OrderType.ORDER_TYPE_TAILORING:
                self.delivery_method = self.DELIVERY_METHOD_BRANCH
            elif order_type_name == OrderType.ORDER_TYPE_INSTALLATION:
                self.delivery_method = self.DELIVERY_METHOD_HOME
                if not self.delivery_address and hasattr(self, 'customer'):
                    self.delivery_address = self.customer.address

        # التحقق من العنوان للتوصيل المنزلي
        if self.delivery_method == self.DELIVERY_METHOD_HOME and not self.delivery_address:
            raise ValidationError({
                'delivery_address': _('عنوان التسليم مطلوب لخدمة التوصيل للمنزل')
            })

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    def calculate_final_price(self):
        """حساب السعر النهائي للطلب"""
        from django.db.models import F, Sum, ExpressionWrapper, DecimalField

        if hasattr(self, 'items'):
            total = self.items.aggregate(
                total=Sum(
                    ExpressionWrapper(
                        F('quantity') * F('unit_price'),
                        output_field=DecimalField()
                    )
                )
            )['total'] or Decimal('0.00')
            self.final_price = total
            return self.final_price
        return Decimal('0.00')

    def notify_status_change(self, old_status, new_status, changed_by=None, notes=''):
        """إرسال إشعار عند تغيير حالة تتبع الطلب"""
        status_messages = {
            'pending': _('الطلب في قائمة الانتظار'),
            'processing': _('جاري معالجة الطلب'),
            'warehouse': _('الطلب في المستودع'),
            'factory': _('الطلب في المصنع'),
            'cutting': _('قيد القص'),
            'ready': _('الطلب جاهز للتسليم'),
            'delivered': _('تم تسليم الطلب'),
        }
        title = _('تحديث حالة الطلب #{}'.format(self.order_number))
        message = _('{}\nتم تغيير الحالة من {} إلى {}'.format(
            status_messages.get(new_status, ''),
            self.get_tracking_status_display(),
            dict(self.TRACKING_STATUS_CHOICES)[new_status]
        ))
        if notes:
            message += f'\nملاحظات: {notes}'
        # إنشاء سجل لتغيير الحالة
        OrderStatusLog.objects.create(
            order=self,
            old_status=old_status,
            new_status=new_status,
            changed_by=changed_by,
            notes=notes
        )
        # إرسال إشعار للعميل
        send_notification(
            title=title,
            message=message,
            sender=changed_by or self.created_by,
            sender_department_code='orders',
            target_department_code='customers',
            target_branch=self.branch,
            priority='high' if new_status in ['ready', 'delivered'] else 'medium',
            related_object=self
        )
    def send_status_notification(self):
        """إرسال إشعار عند تغيير حالة الطلب"""
        status_messages = {
            'pending': _('تم إنشاء طلب جديد وهو قيد الانتظار'),
            'processing': _('تم بدء العمل على الطلب'),
            'completed': _('تم إكمال الطلب بنجاح'),
            'cancelled': _('تم إلغاء الطلب')
        }
        title = _('تحديث حالة الطلب #{}'.format(self.order_number))
        message = status_messages.get(self.status, _('تم تحديث حالة الطلب'))
        # إرسال إشعار للعميل
        send_notification(
            title=title,
            message=message,
            sender=self.created_by,  # استخدام created_by فقط لأن last_modified_by غير موجود
            sender_department_code='orders',
            target_department_code='customers',
            target_branch=self.branch,
            priority='high' if self.status in ['completed', 'cancelled'] else 'medium',
            related_object=self
        )
    def __str__(self):
        return f'{self.order_number} - {self.customer.name}'
    @property
    def remaining_amount(self):
        """Calculate remaining amount to be paid"""
        return self.total_amount - self.paid_amount
    @property
    def is_fully_paid(self):
        """Check if order is fully paid"""
        return self.paid_amount >= self.total_amount
@receiver(post_save, sender=Order)
def order_post_save(sender, instance, created, **kwargs):
    """دالة تعمل بعد حفظ الطلب مباشرة"""
    if not created and instance.tracker.has_changed('status'):
        instance.send_status_notification()
class OrderItem(models.Model):
    ITEM_TYPE_PRODUCT = 'product'
    ITEM_TYPE_SERVICE = 'service'
    
    ITEM_TYPE_CHOICES = [
        (ITEM_TYPE_PRODUCT, _('منتج')),
        (ITEM_TYPE_SERVICE, _('خدمة')),
    ]

    order = models.ForeignKey(
        Order,
        related_name='items',
        on_delete=models.CASCADE,
        verbose_name=_('الطلب')
    )
    item_type = models.CharField(
        max_length=20,
        choices=ITEM_TYPE_CHOICES,
        default=ITEM_TYPE_PRODUCT,
        verbose_name=_('نوع العنصر')
    )
    product = models.ForeignKey(
        'inventory.Product',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        verbose_name=_('المنتج')
    )
    quantity = models.PositiveIntegerField(
        default=1,
        verbose_name=_('الكمية')
    )
    unit_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name=_('سعر الوحدة')
    )

    class Meta:
        verbose_name = _('عنصر الطلب')
        verbose_name_plural = _('عناصر الطلب')
        indexes = [
            models.Index(fields=['order', 'item_type']),
            models.Index(fields=['product']),
        ]

    def __str__(self):
        return f"{self.get_item_type_display()} - {self.quantity} x {self.unit_price}"

    @property
    def total_price(self):
        """حساب السعر الإجمالي للعنصر"""
        return self.quantity * self.unit_price

    def clean(self):
        """التحقق من صحة البيانات"""
        if self.item_type == self.ITEM_TYPE_PRODUCT and not self.product:
            raise ValidationError({
                'product': _('يجب اختيار منتج عندما يكون نوع العنصر منتج')
            })

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)
        
        # تحديث السعر النهائي للطلب
        if self.order:
            self.order.calculate_final_price()
            self.order.save()
class Payment(models.Model):
    PAYMENT_METHOD_CHOICES = [
        ('cash', 'نقداً'),
        ('bank_transfer', 'تحويل بنكي'),
        ('check', 'شيك'),
    ]
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='payments', verbose_name='الطلب')
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='المبلغ')
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, default='cash', verbose_name='طريقة الدفع')
    payment_date = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الدفع')
    reference_number = models.CharField(max_length=100, blank=True, verbose_name='رقم المرجع')
    notes = models.TextField(blank=True, verbose_name='ملاحظات')
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, verbose_name='تم الإنشاء بواسطة')
    class Meta:
        verbose_name = 'دفعة'
        verbose_name_plural = 'الدفعات'
        ordering = ['-payment_date']
        indexes = [
            models.Index(fields=['order'], name='payment_order_idx'),
            models.Index(fields=['payment_method'], name='payment_method_idx'),
            models.Index(fields=['payment_date'], name='payment_date_idx'),
            models.Index(fields=['created_by'], name='payment_created_by_idx'),
        ]
    def __str__(self):
        return f'{self.order.order_number} - {self.amount} ({self.get_payment_method_display()})'
    def save(self, *args, **kwargs):
        """Update order's paid amount when payment is saved"""
        try:
            # التحقق من أن الطلب له مفتاح أساسي
            if not self.order.pk:
                raise models.ValidationError('يجب حفظ الطلب أولاً قبل إنشاء دفعة')
            super().save(*args, **kwargs)
            # Update order's paid amount
            try:
                total_payments = Payment.objects.filter(order=self.order).aggregate(
                    total=models.Sum('amount')
                )['total'] or 0
                self.order.paid_amount = total_payments
                self.order.save(update_fields=['paid_amount'])
                print(f"Successfully updated paid amount for order {self.order.order_number}")
            except Exception as e:
                print(f"Error updating order paid amount: {e}")
                # لا نريد إيقاف العملية إذا فشل تحديث المبلغ المدفوع
        except Exception as e:
            print(f"Error saving payment: {e}")
            raise
class OrderStatusLog(models.Model):
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='status_logs',
        verbose_name=_('الطلب')
    )
    old_status = models.CharField(
        max_length=20,
        choices=Order.STATUS_CHOICES,
        verbose_name=_('الحالة القديمة')
    )
    new_status = models.CharField(
        max_length=20,
        choices=Order.STATUS_CHOICES,
        verbose_name=_('الحالة الجديدة')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ التغيير')
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('تم التغيير بواسطة')
    )
    notes = models.TextField(
        blank=True,
        verbose_name=_('ملاحظات')
    )

    class Meta:
        verbose_name = _('سجل حالة الطلب')
        verbose_name_plural = _('سجلات حالة الطلب')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['order', 'new_status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.order.order_number} - {self.get_new_status_display()}"


@receiver(post_save, sender=Order)
def order_status_changed(sender, instance, created, **kwargs):
    """تسجيل تغييرات حالة الطلب"""
    if instance.tracker.has_changed('status'):
        OrderStatusLog.objects.create(
            order=instance,
            old_status=instance.tracker.previous('status') or instance.STATUS_PENDING,
            new_status=instance.status,
            created_by=instance.created_by
        )
