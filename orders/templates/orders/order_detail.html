{% extends 'base.html' %}

{% block title %}تفاصيل الطلب - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-shopping-cart"></i> تفاصيل الطلب #{{ order.order_number }}</h2>
        <div>
            <a href="{% url 'orders:order_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة للقائمة
            </a>
            <a href="{% url 'orders:order_update' order.pk %}" class="btn btn-primary">
                <i class="fas fa-edit"></i> تعديل الطلب
            </a>
            <a href="{% url 'orders:order_delete' order.pk %}" class="btn btn-danger">
                <i class="fas fa-trash"></i> حذف الطلب
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Order Details -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">معلومات الطلب</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 40%">رقم الطلب</th>
                            <td>{{ order.order_number }}</td>
                        </tr>
                        <tr>
                            <th>العميل</th>
                            <td>
                                <a href="{% url 'customers:customer_detail' order.customer.pk %}">
                                    {{ order.customer.name }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>البائع</th>
                            <td>{% if order.salesperson %}{{ order.salesperson }}{% else %}-{% endif %}</td>
                        </tr>
                        <tr>
                            <th>تاريخ الطلب</th>
                            <td>{{ order.order_date|date:"Y-m-d H:i" }}</td>
                        </tr>
                        <tr>
                            <th>تاريخ التسليم</th>
                            <td>{{ order.delivery_date|date:"Y-m-d"|default:"غير محدد" }}</td>
                        </tr>
                        <tr>
                            <th>حالة الطلب</th>
                            <td>
                                {% if order.status == 'pending' %}
                                <span class="badge bg-warning">قيد الانتظار</span>
                                {% elif order.status == 'processing' %}
                                <span class="badge bg-info">قيد التنفيذ</span>
                                {% elif order.status == 'completed' %}
                                <span class="badge bg-success">مكتمل</span>
                                {% elif order.status == 'cancelled' %}
                                <span class="badge bg-danger">ملغي</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>المبلغ الإجمالي</th>
                            <td>{{ order.total_amount }} {{ currency_symbol }}</td>
                        </tr>
                        <tr>
                            <th>المبلغ المدفوع</th>
                            <td>{{ order.paid_amount }} {{ currency_symbol }}</td>
                        </tr>
                        <tr>
                            <th>المبلغ المتبقي</th>
                            <td>{{ order.remaining_amount }} {{ currency_symbol }}</td>
                        </tr>
                        <tr>
                            <th>تم الإنشاء بواسطة</th>
                            <td>
                                {% if order.created_by %}
                                    {{ order.created_by.get_full_name|default:order.created_by.username }}
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>تاريخ الإنشاء</th>
                            <td>{{ order.created_at|date:"Y-m-d H:i" }}</td>
                        </tr>
                        <tr>
                            <th>تاريخ التحديث</th>
                            <td>{{ order.updated_at|date:"Y-m-d H:i" }}</td>
                        </tr>

                    </table>
                </div>
            </div>
        </div>

        <!-- Customer Information -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">معلومات العميل</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 40%">الاسم</th>
                            <td>{{ order.customer.name }}</td>
                        </tr>
                        <tr>
                            <th>رقم الهاتف</th>
                            <td>{{ order.customer.phone }}</td>
                        </tr>
                        <tr>
                            <th>البريد الإلكتروني</th>
                            <td>{{ order.customer.email|default:"غير متوفر" }}</td>
                        </tr>
                        <tr>
                            <th>العنوان</th>
                            <td>{{ order.customer.address|default:"غير متوفر" }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Notes -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">ملاحظات</h5>
                </div>
                <div class="card-body">
                    {% if order.notes %}
                    <p class="mb-0">{{ order.notes|linebreaks }}</p>
                    {% else %}
                    <p class="text-muted mb-0">لا توجد ملاحظات</p>
                    {% endif %}
                </div>
            </div>

            <!-- Order Type Information -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">معلومات نوع الطلب</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 40%">نوع الطلب</th>
                            <td>{{ order.get_order_type_display }}</td>
                        </tr>
                        {% if order.goods_type %}
                        <tr>
                            <th>نوع البضاعة</th>
                            <td>{{ order.get_goods_type_display }}</td>
                        </tr>
                        {% endif %}
                        {% if order.service_types %}
                        <tr>
                            <th>أنواع الخدمات</th>
                            <td>
                                {% for service_type in order.service_types %}
                                    {% if service_type == 'installation' %}
                                        <span class="badge bg-info">تركيب</span>
                                    {% elif service_type == 'inspection' %}
                                        <span class="badge bg-primary">معاينة</span>
                                    {% elif service_type == 'transport' %}
                                        <span class="badge bg-secondary">نقل</span>
                                    {% elif service_type == 'tailoring' %}
                                        <span class="badge bg-warning">تفصيل</span>
                                    {% endif %}
                                {% endfor %}
                            </td>
                        </tr>
                        {% endif %}
                        {% if order.invoice_number %}
                        <tr>
                            <th>رقم الفاتورة</th>
                            <td>{{ order.invoice_number }}</td>
                        </tr>
                        {% endif %}
                        {% if order.contract_number %}
                        <tr>
                            <th>رقم العقد</th>
                            <td>{{ order.contract_number }}</td>
                        </tr>
                        {% endif %}
                        <tr>
                            <th>تم التحقق من الدفع</th>
                            <td>
                                {% if order.payment_verified %}
                                    <span class="badge bg-success">نعم</span>
                                {% else %}
                                    <span class="badge bg-danger">لا</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>الفرع</th>
                            <td>{{ order.branch.name }}</td>
                        </tr>
                        {% if 'inspection' in order.service_types and order.inspections.exists %}
                        <tr>
                            <th>ملف المعاينة</th>
                            <td>
                                {% for inspection in order.inspections.all %}
                                    {% if inspection.is_uploaded_to_drive and inspection.google_drive_file_url %}
                                        <a href="{{ inspection.google_drive_file_url }}" target="_blank" title="عرض ملف المعاينة {{ forloop.counter }} في Google Drive">
                                            <i class="fas fa-file-pdf text-danger" style="font-size: 20px;"></i>
                                        </a>
                                    {% elif inspection.inspection_file %}
                                        <span title="جاري رفع ملف المعاينة {{ forloop.counter }} إلى Google Drive">
                                            <i class="fas fa-file-pdf text-warning" style="font-size: 20px;"></i>
                                            <i class="fas fa-clock text-warning" style="font-size: 10px; position: relative; top: -8px; left: -3px;"></i>
                                        </span>
                                    {% endif %}
                                    {% if not forloop.last %}<br>{% endif %}
                                {% endfor %}
                            </td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج تحديث الحالة -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">تحديث حالة الطلب</h5>
        </div>
        <div class="card-body">
            <form method="post" action="{% url 'orders:update_status' order.id %}">
                {% csrf_token %}
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="status" class="form-label">الحالة الجديدة*</label>
                            <select name="status" id="status" class="form-select" required>
                                {% for value, label in order.TRACKING_STATUS_CHOICES %}
                                    <option value="{{ value }}" {% if value == order.tracking_status %}selected{% endif %}>
                                        {{ label }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea name="notes" id="notes" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                </div>
                <div class="text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> تحديث الحالة
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- سجل تغييرات الحالة -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">سجل تغييرات الحالة</h5>
        </div>
        <div class="card-body">
            <div class="timeline">
                {% for log in order.status_logs.all %}
                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h6 class="mb-0">{{ log.get_new_status_display }}</h6>
                        <small class="text-muted">
                            {{ log.created_at|date:"Y-m-d H:i" }} -
                            بواسطة {{ log.changed_by.get_full_name|default:log.changed_by.username }}
                        </small>
                        {% if log.notes %}
                        <p class="mb-0 mt-2">{{ log.notes }}</p>
                        {% endif %}
                    </div>
                </div>
                {% empty %}
                <p class="text-muted">لا يوجد سجل لتغييرات الحالة</p>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Order Items -->
    <div class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0">عناصر الطلب</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>المنتج</th>
                            <th>الكمية</th>
                            <th>سعر الوحدة</th>
                            <th>السعر الإجمالي</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in order_items %}
                        <tr>
                            <td>{{ forloop.counter }}</td>
                            <td>{{ item.product.name }}</td>
                            <td>{{ item.quantity }}</td>
                            <td>{{ item.unit_price }} {{ currency_symbol }}</td>
                            <td>{{ item.total_price }} {{ currency_symbol }}</td>
                            <td>{{ item.notes|default:"-" }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center py-3">لا توجد عناصر في هذا الطلب</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr class="table-light">
                            <th colspan="4" class="text-start">المجموع</th>
                            <th>{{ order.total_amount }} {{ currency_symbol }}</th>
                            <td></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

    {% if 'inspection' in order.service_types %}
    <!-- Inspection Details -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">تفاصيل المعاينة</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead>
                        <tr>
                            <th>رقم المعاينة</th>
                            <th>تاريخ الطلب</th>
                            <th>تاريخ التنفيذ</th>
                            <th>عدد الشبابيك</th>
                            <th>فني المعاينة</th>
                            <th>الحالة</th>
                            <th>النتيجة</th>
                            <th>ملف المعاينة</th>
                            <th>البائع</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for inspection in inspections %}
                        <tr>
                            <td>{{ inspection.id }}</td>
                            <td>{{ inspection.request_date|date:"Y-m-d" }}</td>
                            <td>{{ inspection.scheduled_date|date:"Y-m-d" }}</td>
                            <td>{{ inspection.windows_count|default:"-" }}</td>
                            <td>
                                {% if inspection.inspector %}
                                    {% if inspection.inspector.get_full_name %}
                                        {{ inspection.inspector.get_full_name }}
                                    {% else %}
                                        {{ inspection.inspector.username }}
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge {% if inspection.status == 'pending' %}bg-warning
                                           {% elif inspection.status == 'completed' %}bg-success
                                           {% else %}bg-danger{% endif %}">
                                    {{ inspection.get_status_display }}
                                </span>
                            </td>
                            <td>
                                {% if inspection.result %}
                                    <span class="badge {% if inspection.result == 'passed' %}bg-success
                                               {% else %}bg-danger{% endif %}">
                                        {{ inspection.get_result_display }}
                                    </span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if inspection.is_uploaded_to_drive and inspection.google_drive_file_url %}
                                    <a href="{{ inspection.google_drive_file_url }}" target="_blank" title="عرض ملف المعاينة في Google Drive">
                                        <i class="fas fa-file-pdf text-danger" style="font-size: 20px;"></i>
                                    </a>
                                {% elif inspection.inspection_file %}
                                    <span title="جاري رفع الملف إلى Google Drive">
                                        <i class="fas fa-file-pdf text-warning" style="font-size: 20px;"></i>
                                        <i class="fas fa-clock text-warning" style="font-size: 10px; position: relative; top: -8px; left: -3px;"></i>
                                    </span>
                                {% else %}
                                    <i class="fas fa-file-pdf text-muted" style="font-size: 20px;" title="لا يوجد ملف"></i>
                                {% endif %}
                            </td>
                            <td>{% if inspection.responsible_employee %}{{ inspection.responsible_employee }}{% else %}-{% endif %}</td>
                            <td>
                                <a href="{% url 'inspections:inspection_detail' inspection.pk %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if not inspection.status == 'completed' or not inspection.result %}
                                <a href="{% url 'inspections:inspection_update' inspection.pk %}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="10" class="text-center py-3">لم يتم إنشاء معاينة لهذا الطلب بعد</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Payments -->
    <div class="card">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0">الدفعات</h5>
            <a href="{% url 'orders:payment_create' order.pk %}" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> تسجيل دفعة
            </a>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>المبلغ</th>
                            <th>طريقة الدفع</th>
                            <th>تاريخ الدفع</th>
                            <th>رقم المرجع</th>
                            <th>ملاحظات</th>
                            <th>تم الإنشاء بواسطة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in payments %}
                        <tr>
                            <td>{{ forloop.counter }}</td>
                            <td>{{ payment.amount }} {{ currency_symbol }}</td>
                            <td>{{ payment.get_payment_method_display }}</td>
                            <td>{{ payment.payment_date|date:"Y-m-d H:i" }}</td>
                            <td>{{ payment.reference_number|default:"-" }}</td>
                            <td>{{ payment.notes|default:"-" }}</td>
                            <td>
                                {% if payment.created_by %}
                                    {{ payment.created_by.get_full_name|default:payment.created_by.username }}
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'orders:payment_delete' payment.pk %}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center py-3">لا توجد دفعات مسجلة لهذا الطلب</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr class="table-light">
                            <th colspan="7" class="text-start">المجموع المدفوع</th>
                            <th>{{ order.paid_amount }} {{ currency_symbol }}</th>
                        </tr>
                        <tr class="table-light">
                            <th colspan="7" class="text-start">المبلغ المتبقي</th>
                            <th>{{ order.remaining_amount }} {{ currency_symbol }}</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
/* Timeline styling */
.timeline {
    position: relative;
    padding: 20px 0;
}

.timeline-item {
    position: relative;
    padding-left: 40px;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: 0;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #007bff;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #007bff;
}

.timeline-item:not(:last-child):before {
    content: '';
    position: absolute;
    left: 5px;
    top: 12px;
    height: calc(100% + 8px);
    width: 2px;
    background-color: #e9ecef;
}

.timeline-content {
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
}
</style>
{% endblock %}
