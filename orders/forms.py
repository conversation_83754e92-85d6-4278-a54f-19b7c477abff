from django import forms
from django.utils.translation import gettext_lazy as _
from .models import Order, OrderItem, Payment, OrderType
from accounts.models import Salesperson, Branch

class OrderItemForm(forms.ModelForm):
    class Meta:
        model = OrderItem
        fields = ['product', 'quantity', 'unit_price', 'item_type']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-select form-select-sm'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control form-control-sm'}),
            'unit_price': forms.NumberInput(attrs={'class': 'form-control form-control-sm'}),
            'item_type': forms.Select(attrs={'class': 'form-select form-select-sm'}),
        }

class PaymentForm(forms.ModelForm):
    class Meta:
        model = Payment
        fields = ['amount', 'payment_method', 'reference_number', 'notes']
        widgets = {
            'amount': forms.NumberInput(attrs={'class': 'form-control form-control-sm'}),
            'payment_method': forms.Select(attrs={'class': 'form-select form-select-sm'}),
            'reference_number': forms.TextInput(attrs={'class': 'form-control form-control-sm'}),
            'notes': forms.Textarea(attrs={'class': 'form-control form-control-sm', 'rows': 2}),
        }

# Formset for managing multiple order items
OrderItemFormSet = forms.inlineformset_factory(
    Order,
    OrderItem,
    form=OrderItemForm,
    extra=1,
    can_delete=True,
)

class OrderForm(forms.ModelForm):
    # Status field using model's choices
    status = forms.ChoiceField(
        choices=Order.STATUS_CHOICES,
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'}),
        required=False
    )

    # Order type using OrderType's choices
    order_type = forms.ModelChoiceField(
        queryset=OrderType.objects.all(),
        widget=forms.Select(attrs={
            'class': 'form-select form-select-sm',
            'id': 'id_order_type'
        })
    )

    # Contract fields
    contract_number = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control form-control-sm',
            'id': 'id_contract_number'
        })
    )
    contract_file = forms.FileField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control form-control-sm',
            'id': 'id_contract_file',
            'accept': '.pdf'
        })
    )

    salesperson = forms.ModelChoiceField(
        queryset=Salesperson.objects.filter(is_active=True),
        label='البائع',
        required=True,
        widget=forms.Select(attrs={'class': 'form-select form-select-sm'})
    )

    class Meta:
        model = Order
        fields = [
            'customer', 'order_type', 'contract_number', 'contract_file',
            'delivery_method', 'delivery_address', 'branch', 'notes'
        ]

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # تطبيق الأنماط على الحقول
        for field in self.fields:
            if isinstance(self.fields[field].widget, forms.Select):
                self.fields[field].widget.attrs['class'] = 'form-select'
            else:
                self.fields[field].widget.attrs['class'] = 'form-control'

        # التحقق من صلاحيات المستخدم للفروع
        if user and not user.is_main_branch_user():
            self.fields['branch'].initial = user.branch
            self.fields['branch'].disabled = True
            self.fields['branch'].queryset = Branch.objects.filter(
                id=user.branch.id
            )

    def clean(self):
        cleaned_data = super().clean()
        order_type = cleaned_data.get('order_type')
        delivery_method = cleaned_data.get('delivery_method')
        delivery_address = cleaned_data.get('delivery_address')

        if order_type:
            if order_type.name in [
                OrderType.ORDER_TYPE_INSTALLATION,
                OrderType.ORDER_TYPE_TAILORING,
                OrderType.ORDER_TYPE_ACCESSORY
            ]:
                if not cleaned_data.get('contract_number'):
                    self.add_error(
                        'contract_number',
                        _('رقم العقد مطلوب لهذا النوع من الطلبات')
                    )
                if not cleaned_data.get('contract_file'):
                    self.add_error(
                        'contract_file',
                        _('ملف العقد مطلوب لهذا النوع من الطلبات')
                    )

            if order_type.name == OrderType.ORDER_TYPE_TAILORING:
                cleaned_data['delivery_method'] = Order.DELIVERY_METHOD_BRANCH
            
            elif order_type.name == OrderType.ORDER_TYPE_INSTALLATION:
                cleaned_data['delivery_method'] = Order.DELIVERY_METHOD_HOME
                if not delivery_address:
                    self.add_error(
                        'delivery_address',
                        _('عنوان التسليم مطلوب للتركيب')
                    )

        if delivery_method == Order.DELIVERY_METHOD_HOME and not delivery_address:
            self.add_error(
                'delivery_address',
                _('عنوان التسليم مطلوب للتوصيل المنزلي')
            )

        return cleaned_data
