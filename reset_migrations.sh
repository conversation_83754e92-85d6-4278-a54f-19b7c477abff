#!/bin/bash

# تعيين متغيرات قاعدة البيانات
DB_NAME="crm_system"
DB_USER="postgres"
PGPASSWORD="5525"
export PGPASSWORD

echo "بدء عملية إعادة تعيين الترحيلات..."

# حذف ملفات الترحيلات
find . -path "*/migrations/[0-9]*.py" -delete
find . -path "*/migrations/*.pyc" -delete

echo "تم حذف ملفات الترحيلات القديمة"

# التأكد من وجود Postgres
if ! command -v psql &> /dev/null; then
    echo "خطأ: PostgreSQL غير مثبت"
    exit 1
fi

# إسقاط وإعادة إنشاء قاعدة البيانات
echo "جاري إعادة إنشاء قاعدة البيانات..."
psql -h localhost -U $DB_USER -c "DROP DATABASE IF EXISTS $DB_NAME;"
if [ $? -ne 0 ]; then
    echo "خطأ في حذف قاعدة البيانات"
    exit 1
fi

psql -h localhost -U $DB_USER -c "CREATE DATABASE $DB_NAME;"
if [ $? -ne 0 ]; then
    echo "خطأ في إنشاء قاعدة البيانات"
    exit 1
fi

# إنشاء وتنفيذ الترحيلات
echo "جاري إنشاء الترحيلات الجديدة..."
python manage.py makemigrations
if [ $? -ne 0 ]; then
    echo "خطأ في إنشاء الترحيلات"
    exit 1
fi

echo "جاري تنفيذ الترحيلات..."
python manage.py migrate
if [ $? -ne 0 ]; then
    echo "خطأ في تنفيذ الترحيلات"
    exit 1
fi

# تنظيف متغير كلمة المرور
unset PGPASSWORD

echo "تمت العملية بنجاح!"
