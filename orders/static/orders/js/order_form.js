document.addEventListener('DOMContentLoaded', function() {
    const orderTypeSelect = document.getElementById('id_order_type');
    const contractFields = document.querySelector('.contract-fields');
    const deliveryMethodSelect = document.getElementById('id_delivery_method');
    const branchSelect = document.getElementById('id_branch');

    function updateFormFields() {
        const selectedType = orderTypeSelect.value;
        
        // إدارة حقول العقد
        if (['INSTALLATION', 'TAILORING', 'ACCESSORY'].includes(selectedType)) {
            contractFields.style.display = 'block';
            document.getElementById('id_contract_number').required = true;
            document.getElementById('id_contract_file').required = true;
        } else {
            contractFields.style.display = 'none';
            document.getElementById('id_contract_number').required = false;
            document.getElementById('id_contract_file').required = false;
        }

        // إدارة طريقة التسليم
        if (selectedType === 'TAILORING') {
            deliveryMethodSelect.value = 'BRANCH';
            deliveryMethodSelect.disabled = true;
        } else if (selectedType === 'INSTALLATION') {
            deliveryMethodSelect.value = 'HOME';
            deliveryMethodSelect.disabled = true;
        } else {
            deliveryMethodSelect.disabled = false;
        }
    }

    orderTypeSelect.addEventListener('change', updateFormFields);
    updateFormFields(); // تشغيل عند تحميل الصفحة
});